import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { useLogout } from "@/hooks/useLogout";
import { AppSidebar } from "@/components/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Bell, Heart, User } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import ScrollToTop from "@/components/ScrollToTop";
import { Navbar } from "@/components/Navbar";

const Dashboard = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const logoutUser = useLogout();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem("token");
    if (!token) {
      toast.error("Please log in to access the dashboard");
      navigate("/login");
      return;
    }

    // For now, just get user info from localStorage if available
    // In a real app, you'd decode the JWT or make an API call
    const userData = localStorage.getItem("user");
    if (userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
  }, [navigate]);

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent mx-auto mb-4" />
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    // <div className="min-h-screen bg-background p-6">
    //   <div className="max-w-4xl mx-auto">
    //     <div className="flex justify-between items-center mb-8">
    //       <h1 className="text-3xl font-bold">Dashboard</h1>
    //       <Button onClick={logoutUser} variant="outline">
    //         Logout
    //       </Button>
    //     </div>

    //     <div className="grid gap-6 md:grid-cols-2">
    //       <Card>
    //         <CardHeader>
    //           <CardTitle>Welcome Back!</CardTitle>
    //         </CardHeader>
    //         <CardContent>
    //           <div className="space-y-2">
    //             <p>
    //               <strong>Username:</strong> {user.username}
    //             </p>
    //             <p>
    //               <strong>Email:</strong> {user.email}
    //             </p>
    //             <p>
    //               <strong>Full Name:</strong> {user.fullName || "Not set"}
    //             </p>
    //             <p>
    //               <strong>Role:</strong> {user.role}
    //             </p>
    //             <p>
    //               <strong>Seller:</strong> {user.isSeller ? "Yes" : "No"}
    //             </p>
    //           </div>
    //         </CardContent>
    //       </Card>

    //       <Card>
    //         <CardHeader>
    //           <CardTitle>Quick Actions</CardTitle>
    //         </CardHeader>
    //         <CardContent>
    //           <div className="space-y-4">
    //             <Button className="w-full" variant="outline">
    //               View Profile
    //             </Button>
    //             <Button className="w-full" variant="outline">
    //               Settings
    //             </Button>
    //             {user.isSeller && (
    //               <Button className="w-full">Seller Dashboard</Button>
    //             )}
    //           </div>
    //         </CardContent>
    //       </Card>
    //     </div>
    //   </div>
    // </div>

    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header
          className={cn(
            "fixed top-0 z-10 w-full border-b backdrop-blur-md transition-all duration-300 px-4 pt-4 pb-1",
            isSidebarOpen ? "lg:w-[calc(100%-256px)] lg:left-64" : "left-0"
          )}
        >
          <div className="flex h-full items-center justify-between gap-4">
            {/* Left Section: Sidebar Toggle + Logo */}
            <div className="flex items-center gap-4">
              <SidebarTrigger
                onClick={() => setIsSidebarOpen((prev) => !prev)}
              />
              <Separator orientation="vertical" className="h-4" />
              <div className="flex items-center gap-2 text-xl font-semibold text-primary">
                <img
                  src="./src/assets/logo.svg"
                  alt="GigGlobe Logo"
                  className="h-6 w-6"
                />
                GigGlobe
              </div>
            </div>

            {/* Center: Search Bar */}

            {/* Right Section: Actions */}
            <div className="flex items-center gap-2">
              <Input
                type="search"
                placeholder="Search for gigs..."
                className="w-sm"
              />

              <Button variant="ghost" size="icon">
                <Heart className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon">
                <Bell className="w-5 h-5" />
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <User className="w-5 h-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logoutUser}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Sub-navigation / Tabs */}
          <div className="mt-5 lg:w-[calc(100%-256px)]">
            <Navbar />
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-6 p-6 mt-28">
          {/* Welcome Section */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold tracking-tight">
              Welcome back, {user.username}!
            </h1>
            <p className="text-muted-foreground">
              Discover amazing services from talented freelancers worldwide
            </p>
          </div>

          {/* Sample Gig Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: "I will design a modern logo for your business",
                seller: "John Designer",
                rating: 4.9,
                reviews: 127,
                price: 2500,
                image: "🎨",
                category: "Design & Creative",
              },
              {
                title: "I will write SEO optimized content for your website",
                seller: "Sarah Writer",
                rating: 4.8,
                reviews: 89,
                price: 1500,
                image: "✍️",
                category: "Writing & Translation",
              },
              {
                title: "I will develop a responsive React website",
                seller: "Mike Developer",
                rating: 5.0,
                reviews: 45,
                price: 15000,
                image: "💻",
                category: "Programming & Tech",
              },
              {
                title: "I will create engaging social media content",
                seller: "Emma Marketer",
                rating: 4.7,
                reviews: 203,
                price: 3000,
                image: "📱",
                category: "Digital Marketing",
              },
              {
                title: "I will edit your video professionally",
                seller: "Alex Editor",
                rating: 4.9,
                reviews: 156,
                price: 5000,
                image: "🎬",
                category: "Video & Animation",
              },
              {
                title: "I will translate documents to multiple languages",
                seller: "Maria Translator",
                rating: 4.8,
                reviews: 78,
                price: 1200,
                image: "🌍",
                category: "Writing & Translation",
              },
            ].map((gig, index) => (
              <Card
                key={index}
                className="overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center text-4xl">
                  {gig.image}
                </div>
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-sm line-clamp-2 mb-1">
                        {gig.title}
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        {gig.category}
                      </p>
                    </div>

                    <div className="flex items-center gap-2 text-xs">
                      <span className="font-medium">{gig.seller}</span>
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-500">★</span>
                        <span className="font-medium">{gig.rating}</span>
                        <span className="text-muted-foreground">
                          ({gig.reviews})
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">
                          Starting at
                        </p>
                        <p className="font-bold text-lg">
                          ₹{gig.price.toLocaleString()}
                        </p>
                      </div>
                      <Button size="sm" className="ml-2">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More Section */}
          <div className="flex justify-center pt-6">
            <Button variant="outline" size="lg">
              Load More Gigs
            </Button>
          </div>

          <ScrollToTop />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default Dashboard;
