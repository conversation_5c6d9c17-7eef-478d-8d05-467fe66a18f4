const mongoose = require("mongoose");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

async function fixIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Get the professions collection
    const db = mongoose.connection.db;
    const collection = db.collection("professions");

    // List all indexes
    console.log("\n📋 Current indexes:");
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, JSON.stringify(index.key));
    });

    // Check if there's a problematic name_1 index
    const nameIndex = indexes.find((index) => index.name === "name_1");
    if (nameIndex) {
      console.log("\n⚠️  Found problematic 'name_1' index");
      console.log("Dropping the 'name_1' index...");
      await collection.dropIndex("name_1");
      console.log("✅ Successfully dropped 'name_1' index");
    } else {
      console.log("\n✅ No problematic 'name_1' index found");
    }

    // List indexes after cleanup
    console.log("\n📋 Indexes after cleanup:");
    const updatedIndexes = await collection.indexes();
    updatedIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, JSON.stringify(index.key));
    });

    console.log("\n✅ Index cleanup completed");
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Disconnected from MongoDB");
  }
}

fixIndexes();
