import { useEffect, useState } from "react";
import axios from "axios";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Link } from "react-router-dom";

export function Navbar() {
  const [professions, setProfessions] = useState([]);

  useEffect(() => {
    const fetchProfessions = async () => {
      try {
        const res = await axios.get(
          "http://localhost:3000/api/admin/professions"
        );
        console.log("Professions response:", res.data); // Debug log
        setProfessions(res.data.data || []); // make sure the response is shaped as expected
      } catch (error) {
        console.error("Error fetching professions:", error);
        // Set empty array on error to prevent crashes
        setProfessions([]);
      }
    };

    fetchProfessions();
  }, []);

  // Debug log to see what data we have
  console.log("Professions in render:", professions);

  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        {professions.length === 0 ? (
          <NavigationMenuItem>
            <span className="text-muted-foreground">
              Loading professions...
            </span>
          </NavigationMenuItem>
        ) : (
          professions.map((profession) => (
            <NavigationMenuItem key={profession._id}>
              <NavigationMenuTrigger>
                <Link to={`/profession/${profession._id}`}>
                  {profession.professionTitle}
                </Link>
              </NavigationMenuTrigger>

              <NavigationMenuContent>
                <ul className="p-4 ">
                  {profession.subcategories &&
                  profession.subcategories.length > 0 ? (
                    profession.subcategories.map((sub) => (
                      <li key={sub._id} className="mb-4">
                        <h2 className="font-semibold text-sm mb-1">
                          {sub.title}
                        </h2>
                        <ul className="ml-2 list-disc text-xs text-muted-foreground">
                          {sub.items && sub.items.length > 0 ? (
                            sub.items.map((item) => (
                              <li key={item._id}>
                                <Link
                                  to={`/${profession.professionTitle}/${sub.title}/${item.name}`}
                                  className="hover:underline"
                                >
                                  {item.name}
                                </Link>
                              </li>
                            ))
                          ) : (
                            <li className="text-muted-foreground">
                              No items available
                            </li>
                          )}
                        </ul>
                      </li>
                    ))
                  ) : (
                    <li className="text-muted-foreground">
                      No subcategories available
                    </li>
                  )}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          ))
        )}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
