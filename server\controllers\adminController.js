const { Profession } = require("../models/Profession");

exports.addProfession = async (req, res) => {
  try {
    const { professionTitle, subcategories } = req.body;

    if (!professionTitle || !subcategories || !Array.isArray(subcategories)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid input data" });
    }

    // Get admin ID from the JWT token (set by verifyAdminMiddleware)
    const adminId = req.user.userId;

    const formattedSubcategories = subcategories.map((subcategory) => ({
      title: subcategory.title,
      addedBy: adminId,
      items: (subcategory.items || []).map((item) => ({
        name: item.name,
        addedBy: adminId,
      })),
    }));

    const newProfession = new Profession({
      professionTitle,
      addedBy: adminId,
      subcategories: formattedSubcategories,
    });

    const saved = await newProfession.save();

    res.status(201).json({
      success: true,
      message: "Profession added successfully",
      data: saved,
    });
  } catch (error) {
    console.error("Error adding profession:", error);
    res
      .status(500)
      .json({ success: false, message: "Server error", error: error.message });
  }
};

exports.getProfessions = async (req, res) => {
  try {
    const professions = await Profession.find({})
      .populate("addedBy", "username email") // Populate admin info with selected fields
      .populate("subcategories.addedBy", "username email")
      .populate("subcategories.items.addedBy", "username email")
      .lean();

    res.status(200).json({
      success: true,
      message: "Professions fetched successfully",
      data: professions,
    });
  } catch (error) {
    console.error("Error fetching professions:", error);
    res
      .status(500)
      .json({ success: false, message: "Server error", error: error.message });
  }
};

exports.addSubCategory = async (req, res) => {
  const { professionId } = req.params;
  const { title, addedBy, items } = req.body;

  try {
    if (!title || !addedBy || !Array.isArray(items)) {
      return res
        .status(400)
        .json({ success: false, message: "Missing fields" });
    }

    // Format items with required fields
    const formattedItems = items.map((item) => ({
      name: item.name,
      addedBy,
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: addedBy,
    }));

    const subcategory = {
      title,
      addedBy,
      createdAt: new Date(),
      updatedAt: new Date(),
      items: formattedItems,
    };

    const updatedProfession = await Profession.findByIdAndUpdate(
      professionId,
      { $push: { subcategories: subcategory } },
      { new: true }
    );

    if (!updatedProfession) {
      return res
        .status(404)
        .json({ success: false, message: "Profession not found" });
    }

    res.status(200).json({
      success: true,
      message: "Subcategory added successfully",
      data: updatedProfession,
    });
  } catch (error) {
    console.error("Error adding subcategory:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};
